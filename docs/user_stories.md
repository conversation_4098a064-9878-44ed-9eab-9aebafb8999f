# User Stories

## Core Functionality
- As a user, I want to brainstorm ideas with AI assistance to explore creative concepts more effectively
- As a user, I want to use an infinite canvas interface to organize and navigate complex information structures with spatial flexibility
- As a new user, I want to start with text-only nodes to focus on content creation before exploring advanced formatting options

## Control & Customization
- As a user, I want a mind-mapping system that combines manual organization with AI generation, while maintaining complete control over content
- As a user, I want to create and edit nodes manually while having AI generate related nodes on demand, maintaining full control over the content structure
- As a power user, I want to specify generation parameters (e.g., "Create 3 sub-nodes about Topic1 focusing on X factor") to guide AI output

## User Experience
- As a user, I need a simple interface with minimal learning curve to quickly start organizing and expanding my ideas
- As a user, I want to edit node content directly with rich text formatting capabilities for clear information hierarchy

## Example Workflow
1. Create root node: "Renewable Energy"
2. Create his child node: "Generate 3 sub-topics focusing on storage solutions"
3. Press tu button to generate:
we generate a new node with:
   - Battery Technology Advances
   - Hydrogen Fuel Cells
   - Pumped Hydro Storage
4. Edit nodes directly to add specific research data

## Similar Products/Inspiration
- Obsidian Canvas
- Google Jamboard
- Figma Whiteboard
- Miro Infinite Canvas
- MindMeister

## Key Differentiators
- Focus on AI-assisted expansion rather than manual creation
- Text-first approach with progressive complexity
- Balance between free-form canvas and structured hierarchy


# MVP User Stories

## Core Flow
1. User creates text node
2. Clicks "Generate AI" 
3. AI creates 1 connected nodes
4. User edits/manually arranges

## Essential Features
- Text node creation
- AI generation button
- Basic drag/zoom
- Export as JSON

## Simplified UI Requirements
1. Floating toolbar with:
   - Add node
   - Generate AI
2. Right-click node menu
3. Canvas navigation controls



-------------

as a user i want to be able to be posible to paste big chunks of text like 20000 words or more on the blocks,
right now when i paste the large text, the  block grow to big and is not human readeable, 
i think the block should maintain his size , and we can scroll down the block to see all the content, DONE X
   sub problem: the scrol bar of each block is activated by defect(i mean when i move the mouse in the block , ), i mean i want  to intereact with the block when only i seletec



as a user i want to be able to remove the arrow, i mean i clik on the arrwo i press delete and the arrow disapear Done X 


----

as a user i want to be able to eddit the text size of the blocks so i have better comfort

---

as a user i want the generation of the new nodes be maded by the ai , be more organized, for example i click on the button "generate AI" and the AI generate 3 new nodes in the bottom of the original node, like his X children, going from left to rigth, in the bottom , ask me first if you undertandt

---
bug: as a user i want to be able to connect the nodes using the arrow, but the grab hand  that makes appear the skyblue dots so i can create the arrow, do not appear , seems like is being hidded, in only apppear in the top left corder of the oeach block 


---

as a user i want a better ui, i mean lets when i create a new arrow and i put the arrow in a empty space, this mean i want to create a node in this place, this is like a standand feature in canvas 